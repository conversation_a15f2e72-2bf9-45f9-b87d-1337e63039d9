/**
 * Widget样式加载器
 * 用于管理Widget在Shadow DOM中的样式加载
 */

// 样式缓存
let cachedWidgetStyles: string | null = null

/**
 * 加载Widget样式文件
 * @param shadowRoot Shadow DOM根节点
 * @returns Promise<boolean> 是否成功加载
 */
export async function loadWidgetStyles(shadowRoot: ShadowRoot): Promise<boolean> {
  try {
    // 如果已有缓存，直接使用
    if (cachedWidgetStyles) {
      injectStyleText(shadowRoot, cachedWidgetStyles)
      console.log('✅ 使用缓存的Widget样式')
      return true
    }

    // 尝试加载样式文件
    const stylePaths = [
      // 生产环境可能的路径
      './widget.css',
      './assets/widget.css',
      './style.css',
      './dist-widget/style.css',
    ]

    for (const path of stylePaths) {
      try {
        const response = await fetch(path)
        if (response.ok) {
          const cssText = await response.text()
          cachedWidgetStyles = cssText
          injectStyleText(shadowRoot, cssText)
          console.log(`✅ 成功加载Widget样式: ${path}`)
          return true
        }
      } catch (error) {
        console.warn(`尝试加载样式失败: ${path}`, error)
      }
    }

    return false
  } catch (error) {
    console.error('加载Widget样式时发生错误:', error)
    return false
  }
}

/**
 * 注入样式文本到Shadow DOM
 * @param shadowRoot Shadow DOM根节点
 * @param cssText CSS文本内容
 */
export function injectStyleText(shadowRoot: ShadowRoot, cssText: string): void {
  const styleElement = document.createElement('style')
  styleElement.textContent = cssText
  shadowRoot.appendChild(styleElement)
}

/**
 * 获取基础回退样式
 * @returns 基础CSS样式字符串
 */
export function getFallbackStyles(): string {
  return `
    /* Widget基础回退样式 */
    :host {
      all: initial;
      contain: layout style paint;
    }
    
    * {
      box-sizing: border-box;
    }
    
    .customer-service-widget {
      position: relative;
      z-index: 1;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      font-size: 14px;
      line-height: 1.5;
      color: #333;
      box-sizing: border-box;
      all: initial;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    }
    
    .widget-trigger {
      width: 60px;
      height: 60px;
      background: linear-gradient(135deg, #4c5cec 0%, #5a67d8 100%);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      transition: all 0.3s ease;
      position: relative;
      border: none;
    }
    
    .trigger-icon {
      color: white;
      font-size: 24px;
      width: 24px;
      height: 24px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    
    .widget-expanded {
      position: fixed !important;
      bottom: 20px !important;
      right: 20px !important;
      width: 380px !important;
      height: 600px !important;
      z-index: 999999 !important;
    }
    
    @media (max-width: 768px) {
      .widget-expanded {
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        right: 0 !important;
        bottom: 0 !important;
        width: 100vw !important;
        height: 100vh !important;
      }
    }
  `
}

/**
 * 检查是否有内联样式可用
 * @returns 内联样式文本或null
 */
export function getInlineStyles(): string | null {
  return (window as any).__EASEAI_WIDGET_CSS__ || null
}

/**
 * 为Shadow DOM注入完整的样式
 * @param shadowRoot Shadow DOM根节点
 */
export async function injectWidgetStyles(shadowRoot: ShadowRoot): Promise<void> {
  // 保存Shadow DOM引用到全局，供CSS注入使用
  ;(window as any).__EASEAI_SHADOW_ROOT__ = shadowRoot

  // 1. 尝试使用内联CSS（构建时注入的样式）
  const inlinedCSS = getInlineStyles()
  if (inlinedCSS) {
    injectStyleText(shadowRoot, inlinedCSS)
    console.log('✅ 使用内联CSS样式')
    return
  }

  // 2. 在开发环境中，Vue组件的样式会被自动处理，这里直接使用回退样式
  console.log('使用基础回退样式（开发环境或Vue组件样式）')
  injectStyleText(shadowRoot, getFallbackStyles())
}
